import * as admin from "firebase-admin";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { processOrderCancellation } from "./services/order-cancellation-service";
import { log } from "./utils/logger";
import { commonFunctionsConfig } from "./constants";
import { OrderEntity } from "@mikerudenko/marketplace-shared";

const db = admin.firestore();

export async function processExpiredOrders() {
  try {
    log.monitorLog("Starting expired orders processing", {
      monitor: "expired_orders",
      status: "started",
    });

    const now = admin.firestore.Timestamp.now();

    // Only cancel orders where sellers failed to send gifts to relayer (status='paid')
    // Orders with status='gift_sent_to_relayer' are not cancelled - buyers can take gifts after deadline
    const expiredOrdersQuery = db
      .collection("orders")
      .where("status", "==", "paid")
      .where("deadline", "<", now);

    const expiredOrdersSnapshot = await expiredOrdersQuery.get();

    if (expiredOrdersSnapshot.empty) {
      log.monitorLog("No expired orders found", {
        monitor: "expired_orders",
        status: "no_orders",
      });
      return;
    }

    log.monitorLog("Found expired orders to process", {
      monitor: "expired_orders",
      count: expiredOrdersSnapshot.size,
      status: "found_orders",
    });

    for (const orderDoc of expiredOrdersSnapshot.docs) {
      const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

      // Validate order has both buyer and seller
      if (!order.buyerId || !order.sellerId) {
        log.monitorLog("Skipping order: missing buyer or seller", {
          monitor: "expired_orders",
          orderId: order.id,
          status: "skipped",
          reason: "missing_buyer_or_seller",
        });
        continue;
      }

      try {
        const result = await processOrderCancellation(order, order.sellerId);
        log.monitorLog("Successfully processed expired order", {
          monitor: "expired_orders",
          orderId: order.id,
          status: "processed",
          message: result.message,
        });
      } catch (error) {
        log.error("Failed to process expired order", error, {
          monitor: "expired_orders",
          orderId: order.id,
          operation: "process_expired_order",
        });
      }
    }

    log.monitorLog("Expired orders processing completed", {
      monitor: "expired_orders",
      status: "completed",
    });
  } catch (error) {
    log.error("Error in processExpiredOrders", error, {
      monitor: "expired_orders",
      operation: "process_expired_orders",
    });
    throw error;
  }
}

export const expiredOrdersMonitor = onSchedule(
  {
    schedule: "0 0 * * *", // Run daily at midnight UTC
    timeZone: "UTC",
    ...commonFunctionsConfig,
  },
  async () => {
    try {
      log.monitorLog("Expired orders monitor triggered", {
        monitor: "expired_orders",
        status: "triggered",
        timestamp: new Date().toISOString(),
      });
      await processExpiredOrders();
      log.monitorLog("Expired orders monitor completed successfully", {
        monitor: "expired_orders",
        status: "completed",
      });
    } catch (error) {
      log.error("Expired orders monitor failed", error, {
        monitor: "expired_orders",
        status: "monitor_failed",
      });
    }
  }
);
