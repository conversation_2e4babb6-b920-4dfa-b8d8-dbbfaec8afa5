import * as admin from "firebase-admin";
import { log } from "../utils/logger";
import { applyTransactionSign } from "../utils/transaction-sign-utils";
import { TxType, UserTxEntity } from "@mikerudenko/marketplace-shared";

const USER_TX_HISTORY_SUBCOLLECTION = "tx_history";

export interface CreateTransactionParams {
  userId: string;
  txType: TxType;
  amount: number;
  orderId?: string;
  description?: string;
  isReceivingCompensation?: boolean; // For CANCELATION_FEE only
}

export async function createTransactionRecord(
  params: CreateTransactionParams
): Promise<void> {
  const {
    userId,
    txType,
    amount,
    orderId,
    description,
    isReceivingCompensation,
  } = params;

  try {
    // Apply standardized sign to the amount
    const signedAmount = applyTransactionSign(
      amount,
      txType,
      isReceivingCompensation
    );

    const db = admin.firestore();
    const txRecord: UserTxEntity = {
      tx_type: txType,
      user_id: userId,
      amount: signedAmount,
      order_id: orderId,
      description,
      createdAt:
        admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
    };

    // Store transaction as sub-collection under the user
    await db
      .collection("users")
      .doc(userId)
      .collection(USER_TX_HISTORY_SUBCOLLECTION)
      .add(txRecord);

    log.info("Transaction history record created", {
      userId,
      txType,
      originalAmount: amount,
      signedAmount,
      orderId,
      operation: "create_transaction_record",
    });
  } catch (error) {
    log.error("Error creating transaction history record", error, {
      userId,
      txType,
      originalAmount: amount,
      orderId,
      operation: "create_transaction_record",
    });
    // Don't throw error to avoid breaking main operations
    // Transaction history is supplementary data
  }
}
