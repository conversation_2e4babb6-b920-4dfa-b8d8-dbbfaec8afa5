import * as admin from "firebase-admin";
import { lockFundsWithHistory } from "./balance-service";
import {
  validateBuyerPurchase,
  validateSellerPurchase,
} from "./order-validation-service";
import { addDeadlineIfMarketCollection as addDeadlineIfCollectionIsLaunched } from "./deadline-service";
import { UserType, TxType, OrderStatus } from "@mikerudenko/marketplace-shared";

export async function processPurchase(
  db: admin.firestore.Firestore,
  params: {
    userId: string;
    orderId: string;
    userType: UserType;
  }
) {
  const { userId, orderId, userType } = params;

  // Validate purchase based on user type
  const validation =
    userType === UserType.BUYER
      ? await validateBuyerPurchase(db, { userId, orderId })
      : await validateSellerPurchase(db, { userId, orderId });

  const { order, lockedAmount, lockPercentage } = validation;

  // Lock funds for the user
  const txType =
    userType === UserType.BUYER
      ? TxType.BUY_LOCK_COLLATERAL
      : TxType.SELL_LOCK_COLLATERAL;
  await lockFundsWithHistory({
    userId,
    amount: lockedAmount,
    txType,
    orderId,
    description: `Locked collateral for ${userType} (${lockedAmount} TON, ${
      lockPercentage * 100
    }% of ${order.price} TON order)`,
  });

  // Prepare update data
  // If buyer is purchasing an order that already has owned_gift_id,
  // set status directly to GIFT_SENT_TO_RELAYER
  const shouldSkipToPaidStatus = userType === "buyer" && order.owned_gift_id;

  const updateData: any = {
    status: shouldSkipToPaidStatus
      ? OrderStatus.GIFT_SENT_TO_RELAYER
      : OrderStatus.PAID,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  };

  // Set buyer or seller ID based on user type
  if (userType === "buyer") {
    updateData.buyerId = userId;
  } else {
    updateData.sellerId = userId;
  }

  // Add deadline if collection is in MARKET status
  await addDeadlineIfCollectionIsLaunched(
    db,
    order.collectionId,
    orderId,
    updateData
  );

  // Update order
  await db.collection("orders").doc(orderId).update(updateData);

  const actionMessage =
    userType === "buyer"
      ? shouldSkipToPaidStatus
        ? "Gift is ready to be claimed from relayer."
        : "Waiting for seller to send gift."
      : "You can now send the gift.";

  return {
    success: true,
    message: `Purchase successful! ${lockedAmount} TON locked (${
      lockPercentage * 100
    }% of ${order.price} TON order). ${actionMessage}`,
    lockedAmount,
    orderAmount: order.price,
    lockPercentage: lockPercentage * 100,
  };
}
