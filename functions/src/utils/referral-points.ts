import { REFERRAL_TIERS } from "@mikerudenko/marketplace-shared";

export function calculateReferralPoints(referralCount: number): number {
  let totalPoints = 0;

  for (const tier of REFERRAL_TIERS) {
    if (referralCount >= tier.friends) {
      totalPoints += tier.points;
    } else {
      break;
    }
  }

  return totalPoints;
}

export function getUserPoints(
  currentUserPoints: number,
  referralCount: number
): number {
  const calculatedPoints = calculateReferralPoints(referralCount);

  return calculatedPoints;
}

export function getNextTier(referralCount: number): {
  threshold: number;
  points: number;
  remainingReferrals: number;
} | null {
  for (const tier of REFERRAL_TIERS) {
    if (referralCount < tier.friends) {
      return {
        threshold: tier.friends,
        points: tier.points,
        remainingReferrals: tier.friends - referralCount,
      };
    }
  }
  return null;
}
