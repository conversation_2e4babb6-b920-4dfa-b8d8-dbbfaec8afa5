/* eslint-disable indent */
import { TxType } from "@mikerudenko/marketplace-shared";

/**
 * Standardized transaction amount sign rules:
 * - DEPOSIT: Always negative (-) - money leaving user's wallet to platform
 * - WITHDRAW: Always positive (+) - money returning to user's wallet from platform
 * - BUY_LOCK_COLLATERAL: Always negative (-) - buyer's collateral locked when creating buy order
 * - UNLOCK_COLLATERAL: Always positive (+) - collateral returned to user
 * - SELL_LOCK_COLLATERAL: Always negative (-) - seller's collateral locked when creating sell order
 * - REFERRAL_FEE: Always positive (+) - earnings received by referrer
 * - CANCELATION_FEE: Context-dependent sign:
 *   - Positive (+) when receiving compensation for counterparty cancellation
 *   - Negative (-) when paying penalty for own cancellation
 * - REFUND: Always positive (+) - money returned to user
 * - SELL_FULFILLMENT: Always positive (+) - seller receiving buyer's payment upon order completion
 * - RESELL_FEE_EARNINGS: Always positive (+) - earnings from reselling an order
 */

export function applyTransactionSign(
  amount: number,
  txType: TxType,
  isReceivingCompensation?: boolean
): number {
  if (amount < 0) {
    throw new Error("Amount must be positive when applying transaction sign");
  }

  switch (txType) {
    case TxType.DEPOSIT:
    case TxType.BUY_LOCK_COLLATERAL:
    case TxType.SELL_LOCK_COLLATERAL:
      return -amount; // Money leaving user

    case TxType.WITHDRAW:
    case TxType.UNLOCK_COLLATERAL:
    case TxType.REFERRAL_FEE:
    case TxType.REFUND:
    case TxType.SELL_FULFILLMENT:
    case TxType.RESELL_FEE_EARNINGS:
      return amount; // Money coming to user

    case TxType.CANCELATION_FEE:
      if (isReceivingCompensation === undefined) {
        throw new Error(
          "isReceivingCompensation must be specified for CANCELATION_FEE transactions"
        );
      }
      return isReceivingCompensation ? amount : -amount;

    default:
      throw new Error(`Unknown transaction type: ${txType}`);
  }
}
