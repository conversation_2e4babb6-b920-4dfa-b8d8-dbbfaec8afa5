import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { verifyBotToken } from "./services/bot-auth-service";
import { log } from "./utils/logger";
import { commonFunctionsConfig } from "./constants";
import { BotSessionEntity } from "@mikerudenko/marketplace-shared";

const db = admin.firestore();

export const saveUserSessionByBot = onCall<{
  userId: string;
  botToken: string;
  sessionData: {
    pendingOrderId?: string;
    echoMode?: boolean;
  };
}>(commonFunctionsConfig, async (request) => {
  const { userId, botToken, sessionData } = request.data;

  if (!userId) {
    throw new HttpsError("invalid-argument", "User ID is required.");
  }

  if (!botToken) {
    throw new HttpsError("invalid-argument", "Bot token is required.");
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError("permission-denied", "Invalid bot token.");
  }

  try {
    const sessionDoc = db.collection("bot_sessions").doc(userId);
    const existingSession = await sessionDoc.get();

    const sessionEntity: Partial<BotSessionEntity> = {
      id: userId,
      pendingOrderId: sessionData.pendingOrderId,
      echoMode: sessionData.echoMode,
      createdAt: existingSession.exists
        ? existingSession.data()?.createdAt
        : (admin.firestore.FieldValue.serverTimestamp() as any),
      updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
    };

    await sessionDoc.set(sessionEntity, { merge: true });

    log.info("Bot session saved successfully", {
      operation: "save_bot_session",
      userId,
      sessionData,
    });

    return {
      success: true,
      message: "Session saved successfully",
    };
  } catch (error) {
    log.error("Error saving bot session", error, {
      operation: "save_bot_session",
      userId,
    });
    throw new HttpsError("internal", "Failed to save session");
  }
});

export const getUserSessionByBot = onCall<{
  userId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { userId, botToken } = request.data;

  if (!userId) {
    throw new HttpsError("invalid-argument", "User ID is required.");
  }

  if (!botToken) {
    throw new HttpsError("invalid-argument", "Bot token is required.");
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError("permission-denied", "Invalid bot token.");
  }

  try {
    const sessionDoc = await db.collection("bot_sessions").doc(userId).get();

    if (!sessionDoc.exists) {
      log.info("Bot session not found", {
        operation: "get_bot_session",
        userId,
      });
      return {
        success: true,
        session: null,
      };
    }

    const sessionData = sessionDoc.data() as BotSessionEntity;

    log.info("Bot session retrieved successfully", {
      operation: "get_bot_session",
      userId,
    });

    return {
      success: true,
      session: {
        pendingOrderId: sessionData.pendingOrderId,
        echoMode: sessionData.echoMode,
      },
    };
  } catch (error) {
    log.error("Error retrieving bot session", error, {
      operation: "get_bot_session",
      userId,
    });
    throw new HttpsError("internal", "Failed to retrieve session");
  }
});

export const clearUserSessionByBot = onCall<{
  userId: string;
  botToken: string;
}>(commonFunctionsConfig, async (request) => {
  const { userId, botToken } = request.data;

  if (!userId) {
    throw new HttpsError("invalid-argument", "User ID is required.");
  }

  if (!botToken) {
    throw new HttpsError("invalid-argument", "Bot token is required.");
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError("permission-denied", "Invalid bot token.");
  }

  try {
    const sessionDoc = db.collection("bot_sessions").doc(userId);
    await sessionDoc.delete();

    log.info("Bot session cleared successfully", {
      operation: "clear_bot_session",
      userId,
    });

    return {
      success: true,
      message: "Session cleared successfully",
    };
  } catch (error) {
    log.error("Error clearing bot session", error, {
      operation: "clear_bot_session",
      userId,
    });
    throw new HttpsError("internal", "Failed to clear session");
  }
});
