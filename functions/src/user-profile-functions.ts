import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { UserEntity } from "@mikerudenko/marketplace-shared";
import { extractRawTonAddress, prepareUserDataForSave } from "./utils";
import { log } from "./utils/logger";
import { getUserPoints } from "./utils/referral-points";
import { AUTH_ERRORS, GENERIC_ERRORS } from "./constants/error-messages";
import { commonFunctionsConfig } from "./constants";

export const changeUserData = onCall<{
  name?: string;
  tg_id?: string;
  ton_wallet_address?: string;
  raw_ton_wallet_address?: string;
  referrer_id?: string;
  points?: number;
}>(commonFunctionsConfig, async (request) => {
  if (!request.auth) {
    throw new HttpsError(
      "unauthenticated",
      JSON.stringify({
        errorKey: AUTH_ERRORS.UNAUTHENTICATED,
        fallbackMessage: "Authentication required.",
      })
    );
  }

  const {
    name,
    tg_id,
    ton_wallet_address,
    raw_ton_wallet_address,
    referrer_id,
    points,
  } = request.data;
  const userId = request.auth.uid;

  try {
    const db = admin.firestore();

    const userDoc = await db.collection("users").doc(userId).get();
    if (!userDoc.exists) {
      throw new HttpsError(
        "not-found",
        JSON.stringify({
          errorKey: AUTH_ERRORS.USER_NOT_FOUND,
          fallbackMessage: "User not found.",
        })
      );
    }

    const currentUserData = userDoc.data() as UserEntity;

    const updateData: Partial<UserEntity> = {};

    if (name !== undefined) {
      updateData.displayName = name;
    }

    if (tg_id !== undefined) {
      updateData.tg_id = tg_id;
    }

    if (ton_wallet_address !== undefined) {
      updateData.ton_wallet_address = ton_wallet_address;

      if (raw_ton_wallet_address === undefined && ton_wallet_address) {
        const rawAddress = extractRawTonAddress(ton_wallet_address);
        if (rawAddress) {
          updateData.raw_ton_wallet_address = rawAddress;
        }
      }
    }

    if (raw_ton_wallet_address !== undefined) {
      updateData.raw_ton_wallet_address = raw_ton_wallet_address;
    }

    // Check if wallet address is already used by another account
    if (updateData.raw_ton_wallet_address) {
      const existingUserQuery = await db
        .collection("users")
        .where(
          "raw_ton_wallet_address",
          "==",
          updateData.raw_ton_wallet_address
        )
        .get();

      if (!existingUserQuery.empty) {
        const existingUserDoc = existingUserQuery.docs[0];
        const existingUserId = existingUserDoc.id;

        if (existingUserId !== userId) {
          throw new HttpsError(
            "already-exists",
            JSON.stringify({
              errorKey: AUTH_ERRORS.WALLET_ALREADY_USED,
              fallbackMessage:
                "This wallet address is already used in another account.",
            })
          );
        }
      }
    }

    if (points !== undefined) {
      updateData.points = points;
    }

    if (referrer_id && !currentUserData.referrer_id) {
      updateData.referrer_id = referrer_id;
      log.info(`Setting referrer_id for user ${userId}: ${referrer_id}`, {
        operation: "user_profile_update",
        userId,
        referrer_id,
        action: "set_referrer",
      });

      try {
        const referralsQuery = await db
          .collection("users")
          .where("referrer_id", "==", referrer_id)
          .get();

        const referralCount = referralsQuery.size;

        const referrerQuery = await db
          .collection("users")
          .doc(referrer_id)
          .get();

        if (referrerQuery.exists) {
          const referrerData = referrerQuery.data() as UserEntity;
          const currentPoints = referrerData.points ?? 0;

          const newPoints = getUserPoints(currentPoints, referralCount);

          await db.collection("users").doc(referrer_id).update({
            points: newPoints,
          });

          log.info("Updated referrer points", {
            operation: "referrer_points_update",
            referrer_id,
            referrer_user_id: referrer_id,
            referral_count: referralCount,
            previous_points: currentPoints,
            new_points: newPoints,
          });
        } else {
          log.warn(`Referrer with user ID ${referrer_id} not found`, {
            operation: "referrer_points_update",
            referrer_id,
            action: "referrer_not_found",
          });
        }
      } catch (pointsError) {
        log.error("Error updating referrer points", pointsError, {
          operation: "referrer_points_update",
          referrer_id,
          userId,
        });
      }
    } else if (referrer_id && currentUserData.referrer_id) {
      log.info(
        `User ${userId} already has referrer_id: ${currentUserData.referrer_id}, not updating`,
        {
          operation: "user_profile_update",
          userId,
          existing_referrer_id: currentUserData.referrer_id,
          attempted_referrer_id: referrer_id,
          action: "skip_referrer_update",
        }
      );
    }

    const preparedData = prepareUserDataForSave(updateData);

    await db.collection("users").doc(userId).update(preparedData);

    log.info(`User profile updated for ${userId}`, {
      operation: "user_profile_update",
      userId,
      updatedFields: Object.keys(preparedData),
      preparedData,
    });

    return {
      success: true,
      message: "User profile updated successfully",
      updatedFields: Object.keys(preparedData),
    };
  } catch (error) {
    log.error("Error in changeUserData function", error, {
      operation: "user_profile_update",
      userId,
      requestData: request.data,
    });

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
      "internal",
      JSON.stringify({
        errorKey: GENERIC_ERRORS.SERVER_ERROR,
        fallbackMessage:
          (error as any).message ?? "Server error while updating user profile.",
      })
    );
  }
});
