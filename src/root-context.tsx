'use client';

import { getAnalytics, isSupported } from 'firebase/analytics';
import { initializeApp } from 'firebase/app';
import { initializeAppCheck, ReCaptchaV3Provider } from 'firebase/app-check';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getFunctions } from 'firebase/functions';
import { getStorage } from 'firebase/storage';
import { useRouter } from 'next/navigation';
import type { ReactNode } from 'react';
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useLocalStorage } from 'usehooks-ts';

import { getAppConfig } from '@/api/app-config-api';
import { getUserById, getUserRole } from '@/api/auth-api';
import { getActiveCollections, getAllCollections } from '@/api/collection-api';
import { useReferralProcessor } from '@/hooks/use-referral-processor';
import { useTelegramAuth } from '@/hooks/use-telegram-auth';

import { AppIntl } from './components/app-intl';
import enMessages from './components/translations/en.json';
import ruMessages from './components/translations/ru.json';
import uaMessages from './components/translations/ua.json';
import {
  type AppConfigEntity,
  AppLocale,
  type CollectionEntity,
  FIREBASE_REGION,
  LocalStorageKeys,
  type UserEntity,
} from './core.constants';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

const app = initializeApp(firebaseConfig);

if (typeof window !== 'undefined') {
  initializeAppCheck(app, {
    provider: new ReCaptchaV3Provider('mp-secret-key'),
    isTokenAutoRefreshEnabled: true,
  });
}

export const firestore = getFirestore(app);
export const firebaseStorage = getStorage(app);
export const firebaseAuth = getAuth();
export const firebaseFunctions = getFunctions(app, FIREBASE_REGION);
export const analytics = isSupported().then((yes) =>
  yes ? getAnalytics(app) : null,
);

interface RootContextType {
  resetUser: () => void;
  currentUser: UserEntity | null;
  role: string;
  collections: CollectionEntity[];
  appConfig: AppConfigEntity | null;
  locale: AppLocale;
  setCurrentUser: (user: UserEntity | null) => void;
  setRole: (role: string) => void;
  setCollections: (collections: CollectionEntity[]) => void;
  setAppConfig: (config: AppConfigEntity | null) => void;
  setLocale: (locale: AppLocale) => void;
  refreshCollections: () => Promise<void>;
  refreshAppConfig: () => Promise<void>;
  refetchUser: () => Promise<void>;
}

const RootContext = createContext<RootContextType | undefined>(undefined);

export const useRootContext = () => {
  const context = useContext(RootContext);
  if (context === undefined) {
    throw new Error('useRootContext must be used within a RootProvider');
  }
  return context;
};

const detectUserLocale = (): AppLocale => {
  if (typeof window === 'undefined') {
    return AppLocale.en;
  }

  const browserLocale = navigator.language.split('-')[0];
  return Object.values(AppLocale).includes(browserLocale as AppLocale)
    ? (browserLocale as AppLocale)
    : AppLocale.en;
};

export const RootProvider = ({ children }: { children: ReactNode }) => {
  const [role, setRole] = useState('');
  const [currentUser, setCurrentUser] = useState<UserEntity | null>(null);
  const [collections, setCollections] = useState<CollectionEntity[]>([]);
  const [appConfig, setAppConfig] = useState<AppConfigEntity | null>(null);
  const [autoLoginAttempted, setAutoLoginAttempted] = useState(false);
  const [locale, setLocale] = useLocalStorage(
    LocalStorageKeys.LOCALE,
    detectUserLocale(),
  );
  const router = useRouter();

  const { processReferralForUser } = useReferralProcessor();

  const { authenticate, isInTelegram } = useTelegramAuth({
    onSuccess: () => {
      console.log('Auto-login successful');
    },
    onError: (error) => {
      console.log('Auto-login failed:', error);
    },
  });

  const refreshCollections = useCallback(async () => {
    try {
      const collections =
        role === 'admin'
          ? await getAllCollections()
          : await getActiveCollections();
      setCollections(collections);
    } catch (error) {
      console.error('Error loading collections:', error);
    }
  }, [role]);

  const refreshAppConfig = useCallback(async () => {
    try {
      const config = await getAppConfig();
      setAppConfig(config);
    } catch (error) {
      console.error('Error loading app config:', error);
    }
  }, []);

  const refetchUser = useCallback(async () => {
    try {
      const currentAuthUser = firebaseAuth.currentUser;
      if (currentAuthUser) {
        const user = await getUserById(currentAuthUser.uid);
        setCurrentUserWithValidation(user);

        // Process referral ID using the custom hook
        if (user) {
          await processReferralForUser(
            user,
            currentAuthUser.uid,
            setCurrentUserWithValidation,
          );
        }
      } else {
        console.warn('No authenticated user found to refetch');
      }
    } catch (error) {
      console.error('Error refetching user:', error);
    }
  }, [processReferralForUser]);

  useEffect(() => {
    const unsubscribe = firebaseAuth.onAuthStateChanged(async (user) => {
      if (user) {
        const userData = await getUserById(user.uid);
        setCurrentUserWithValidation(userData);

        // Process referral ID using the custom hook
        if (userData) {
          await processReferralForUser(
            userData,
            user.uid,
            setCurrentUserWithValidation,
          );
        }

        getUserRole().then((role) => {
          if (!role) {
            console.warn('No role found for user');
            return;
          }

          setRole(role);
          if (
            role !== 'admin' &&
            typeof window !== 'undefined' &&
            window.location.pathname.startsWith('/admin')
          ) {
            router.push('/');
          }
        });
      }
    });

    return () => unsubscribe();
  }, [router, processReferralForUser]);

  useEffect(() => {
    refreshCollections();
    refreshAppConfig();
  }, [refreshCollections, refreshAppConfig]);

  // Refresh collections when role changes
  useEffect(() => {
    if (role) {
      refreshCollections();
    }
  }, [role, refreshCollections]);

  useEffect(() => {
    if (!autoLoginAttempted && !currentUser && isInTelegram) {
      const timer = setTimeout(async () => {
        try {
          setAutoLoginAttempted(true);
          await authenticate();
        } catch (error) {
          console.log('Auto-login attempt failed:', error);
        }
      }, 1000); // Wait 1 seconds

      return () => clearTimeout(timer);
    }
  }, [currentUser, autoLoginAttempted, isInTelegram, authenticate]);

  const setCurrentUserWithValidation = (user: UserEntity | null) => {
    if (user) {
      if (!user.balance) {
        user.balance = {
          sum: 0,
          locked: 0,
        };
      } else {
        if (typeof user.balance.sum !== 'number') {
          user.balance.sum = 0;
        }
        if (typeof user.balance.locked !== 'number') {
          user.balance.locked = 0;
        }
      }
    }
    setCurrentUser(user);
  };

  const resetUser = () => {
    setRole('');
    setCurrentUser(null);
    setCollections([]);
    setAppConfig(null);
  };

  const contextValue = useMemo(
    () => ({
      resetUser,
      currentUser,
      locale,
      role,
      collections,
      appConfig,
      setCurrentUser: setCurrentUserWithValidation,
      setRole,
      setCollections,
      setAppConfig,
      setLocale,
      refreshCollections,
      refreshAppConfig,
      refetchUser,
    }),
    [
      currentUser,
      role,
      collections,
      appConfig,
      locale,
      refreshCollections,
      refreshAppConfig,
      refetchUser,
    ],
  );

  return (
    <RootContext.Provider value={contextValue}>
      <AppIntl {...{ enMessages, ruMessages, uaMessages }}>{children}</AppIntl>
    </RootContext.Provider>
  );
};
