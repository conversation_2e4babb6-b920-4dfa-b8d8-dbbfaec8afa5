import type {
  AppConfigEntity,
  UserBalance,
  UserEntity,
} from '@mikerudenko/marketplace-shared';

export interface BalanceInfo {
  balance: UserBalance;
  availableBalance: number;
}

export function getReferralFeeRate(
  user: UserEntity,
  appConfig: AppConfigEntity | null,
): number {
  if (user.referral_fee && user.referral_fee > 0) {
    return user.referral_fee;
  }
  return appConfig?.referrer_fee || 0;
}

export function getBalanceInfo(user: UserEntity): BalanceInfo {
  const balance = user.balance || { sum: 0, locked: 0 };
  const availableBalance = balance.sum - balance.locked;

  return {
    balance,
    availableBalance,
  };
}

export function formatBPSToPercent(bps: number): string {
  return (bps / 100).toFixed(2);
}

export interface UserBalanceDisplayInfo {
  available: string;
  locked: number;
  hasLocked: boolean;
}

export function getUserBalanceDisplayInfo(
  user: UserEntity | null,
): UserBalanceDisplayInfo {
  if (!user?.balance) {
    return { available: '0.00', locked: 0, hasLocked: false };
  }

  const available = (user.balance.sum - user.balance.locked).toFixed(2);
  const locked = user.balance.locked;
  const hasLocked = locked > 0;

  return { available, locked, hasLocked };
}
