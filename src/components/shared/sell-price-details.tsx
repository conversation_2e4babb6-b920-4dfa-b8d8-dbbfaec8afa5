'use client';

import type { OrderEntity } from '@mikerudenko/marketplace-shared';
import { useIntl } from 'react-intl';

import { TonPriceDisplay } from '@/components/shared/ton-price-display';

import { sellPriceDetailsMessages } from './sell-price-details/intl/sell-price-details.messages';

interface SellPriceDetailsProps {
  order: OrderEntity;
  className?: string;
}

export function SellPriceDetails({
  order,
  className = '',
}: SellPriceDetailsProps) {
  const { formatMessage: t } = useIntl();
  const hasSecondaryPrice =
    order.secondaryMarketPrice && order.secondaryMarketPrice > 0;
  const currentPrice = hasSecondaryPrice
    ? order.secondaryMarketPrice
    : order.price;

  return (
    <div className={`text-center space-y-1 ${className}`}>
      <div className="flex items-center justify-center gap-1">
        <TonPriceDisplay
          amount={currentPrice ?? 0}
          size={24}
          className="text-2xl font-bold text-[#f5f5f5]"
          showUnit
        />
      </div>
      <p className="text-xs text-[#708499]">
        {t(sellPriceDetailsMessages.marketFeesIncluded)}
      </p>
    </div>
  );
}
