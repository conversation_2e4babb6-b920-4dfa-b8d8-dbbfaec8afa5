'use client';

import type {
  CollectionEntity,
  OrderEntity,
} from '@mikerudenko/marketplace-shared';
import { useLocalStorage } from 'usehooks-ts';

import { TgsOrImage } from '@/components/tgs/tgs-or-image';
import { LocalStorageKeys } from '@/core.constants';
import { cn } from '@/lib/utils';

interface OrderImageProps {
  order: OrderEntity;
  collection: CollectionEntity | null | undefined;
  className?: string;
  children?: React.ReactNode;
  badge?: React.ReactNode;
}

export function OrderImage({
  order,
  collection,
  className,
  children,
  badge,
}: OrderImageProps) {
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );

  return (
    <div
      className={cn(
        'aspect-square relative rounded-lg overflow-hidden bg-[#17212b]',
        className,
      )}
    >
      <div className="absolute top-0 left-0 z-50">{badge}</div>
      <TgsOrImage
        isImage={!isAnimatedCollection}
        collectionId={order.collectionId}
        imageProps={{
          alt: collection?.name ?? 'Order item',
          fill: true,
          className: cn(
            'object-cover group-hover:scale-105 transition-transform duration-200 p-4',
          ),
        }}
        tgsProps={{
          style: { height: 'auto', width: 'auto', padding: '16px' },
        }}
      />
      {children}
    </div>
  );
}
