'use client';

import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';

import type { AppConfigEntity, CollectionEntity } from '@/core.constants';
import { getEffectiveLockPeriod } from '@/services/order-service';

import { freezePeriodStatusMessages } from './intl/freeze-period-status.messages';

interface FreezePeriodStatusProps {
  launchedAt?: Date | null;
  collection?: CollectionEntity | null;
  appConfig?: AppConfigEntity | null;
}

interface FreezePeriodInfo {
  status: 'not_started' | 'active' | 'ended';
  label: string;
  timeRemaining?: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  };
}

function calculateFreezePeriodInfo(
  launchedAt?: Date | null,
  collection?: CollectionEntity | null,
  appConfig?: AppConfigEntity | null,
  t?: (message: any, values?: any) => string,
): FreezePeriodInfo {
  if (!launchedAt) {
    return {
      status: 'not_started',
      label: t
        ? t(freezePeriodStatusMessages.freezePeriodNotStarted)
        : "Freeze period hasn't started yet",
    };
  }

  const now = new Date();
  const launchDate = new Date(launchedAt);
  const lockPeriodDays = getEffectiveLockPeriod(collection, appConfig);
  const freezeEndDate = new Date(
    launchDate.getTime() + lockPeriodDays * 24 * 60 * 60 * 1000,
  );

  if (now >= freezeEndDate) {
    return {
      status: 'ended',
      label: t
        ? t(freezePeriodStatusMessages.freezePeriodEnded)
        : 'Freeze period has ended',
    };
  }

  // Calculate time remaining
  const timeDiff = freezeEndDate.getTime() - now.getTime();
  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
  );
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

  return {
    status: 'active',
    label: t
      ? t(freezePeriodStatusMessages.timeRemaining, {
          days,
          hours,
          minutes,
          seconds,
        })
      : `${days}d ${hours}h ${minutes}m ${seconds}s remaining`,
    timeRemaining: { days, hours, minutes, seconds },
  };
}

export function FreezePeriodStatus({
  launchedAt,
  collection,
  appConfig,
}: FreezePeriodStatusProps) {
  const { formatMessage: t } = useIntl();
  const [freezeInfo, setFreezeInfo] = useState<FreezePeriodInfo>(() =>
    calculateFreezePeriodInfo(launchedAt, collection, appConfig, t),
  );

  useEffect(() => {
    const updateFreezeInfo = () => {
      setFreezeInfo(
        calculateFreezePeriodInfo(launchedAt, collection, appConfig, t),
      );
    };

    // Update immediately
    updateFreezeInfo();

    // Update every second for active countdowns
    let interval: NodeJS.Timeout | null = null;
    if (freezeInfo.status === 'active') {
      interval = setInterval(updateFreezeInfo, 1000); // Update every second
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [launchedAt, collection, appConfig, freezeInfo.status]);

  const getStatusColor = () => {
    switch (freezeInfo.status) {
      case 'not_started':
        return 'text-gray-500';
      case 'active':
        return 'text-orange-500';
      case 'ended':
        return 'text-green-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <span className={`text-sm ${getStatusColor()}`}>{freezeInfo.label}</span>
  );
}
