import type { UserEntity } from '@mikerudenko/marketplace-shared';
import { UserType } from '@mikerudenko/marketplace-shared';
import { Avatar } from '@telegram-apps/telegram-ui';
import { Loader2, User } from 'lucide-react';

import { ADMIN_DEFAULT_NAME } from '@/core.constants';

interface UserOrderUserInfoSectionProps {
  otherUser: UserEntity | null;
  userType: UserType;
  loadingUser: boolean;
}

export function UserOrderUserInfoSection({
  otherUser,
  userType,
  loadingUser,
}: UserOrderUserInfoSectionProps) {
  const otherUserRole = userType === UserType.SELLER ? 'Buyer' : 'Seller';

  return (
    <div className="space-y-3">
      <h3 className="font-semibold text-[#f5f5f5] text-center">
        {otherUserRole} Information
      </h3>

      {loadingUser ? (
        <div className="flex items-center justify-center gap-3 p-4 bg-[#232e3c] rounded-2xl">
          <Loader2 className="w-5 h-5 animate-spin text-[#6ab2f2]" />
          <span className="text-[#708499]">Loading user info...</span>
        </div>
      ) : otherUser ? (
        <div className="flex items-center gap-4 p-4 bg-[#232e3c] rounded-2xl">
          {otherUser.photoURL ? (
            <Avatar
              size={48}
              src={otherUser.photoURL}
              className="ring-2 ring-[#6ab2f2]/20"
            />
          ) : (
            <div className="w-12 h-12 bg-[#3a4a5c] rounded-full flex items-center justify-center ring-2 ring-[#6ab2f2]/20">
              <User className="w-6 h-6 text-[#708499]" />
            </div>
          )}
          <div className="flex-1">
            <p className="text-[#f5f5f5] font-semibold text-lg">
              {otherUser.role === 'admin'
                ? ADMIN_DEFAULT_NAME
                : otherUser.displayName || otherUser.name || 'Anonymous User'}
            </p>
            <p className="text-[#6ab2f2] text-sm font-medium">
              {otherUserRole}
            </p>
          </div>
        </div>
      ) : (
        <div className="p-4 bg-[#232e3c] rounded-2xl text-center">
          <p className="text-[#708499] text-sm">
            No {otherUserRole.toLowerCase()} assigned yet
          </p>
        </div>
      )}
    </div>
  );
}
