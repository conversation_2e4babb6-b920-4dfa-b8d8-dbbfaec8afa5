{"actions.apply": "Apply", "actions.buy": "Buy", "actions.cancel": "Cancel", "actions.close": "Close", "actions.confirm": "Confirm", "actions.delete": "Delete", "actions.edit": "Edit", "actions.fulfill": "Fulfill", "actions.pause": "Pause", "actions.reject": "Reject", "actions.resend": "Resend", "actions.save": "Save", "actions.saved": "Saved", "actions.send": "Send", "actions.sent": "<PERSON><PERSON>", "actions.signIn": "Sign in", "actions.signOut": "Sign out", "actions.signUp": "Sign up", "actions.submit": "Submit", "collectionName.unknownCollection": "Unknown Collection", "collectionSelect.collection": "Collection", "collectionSelect.noCollectionsFound": "No collections found matching \"{searchQuery}\".", "collectionSelect.searchCollections": "Search collections...", "collectionSelect.selectCollection": "Select collection...", "countdownPopup.closeNotification": "Close notification", "countdownPopup.depositProcessing": "Deposit Processing", "countdownPopup.minutes": "minutes", "countdownPopup.youWillReceiveFundsWithin": "You will receive your funds within", "depositDrawer.actions.cancel": "Cancel", "depositDrawer.actions.deposit": "<PERSON><PERSON><PERSON><PERSON>", "depositDrawer.actions.pleaseConnectWallet": "Please connect your wallet to make a deposit", "depositDrawer.actions.processing": "Processing...", "depositDrawer.addTonToBalance": "Add TON to your marketplace balance", "depositDrawer.amountInput.amountMustBeAtLeast": "Amount must be at least {amount} TON", "depositDrawer.amountInput.depositAmountTon": "<PERSON><PERSON><PERSON><PERSON> (TON)", "depositDrawer.amountInput.minTonPlaceholder": "Min {amount} TON", "depositDrawer.close": "Close", "depositDrawer.copyTransactionHash": "Copy Transaction Hash", "depositDrawer.depositCompleted": "Your deposit has been completed successfully", "depositDrawer.depositFee": "Deposit fee:", "depositDrawer.depositFunds": "Deposit Funds", "depositDrawer.depositInformation": "Deposit Information", "depositDrawer.depositProcessing": "Deposit Processing", "depositDrawer.depositSuccess": "Deposit Successful!", "depositDrawer.loadingConfiguration": "Loading configuration...", "depositDrawer.minimumDeposit": "Minimum deposit:", "depositDrawer.processingYourDeposit": "Processing your deposit...", "depositDrawer.summary.depositAmount": "Deposit amount:", "depositDrawer.summary.depositFee": "Deposit fee:", "depositDrawer.summary.totalToPay": "Total to pay:", "depositDrawer.transactionHashCopied": "Transaction hash copied to clipboard", "depositDrawer.viewOnTonScan": "View on TON Scan", "depositDrawer.youWillReceiveFundsWithin": "You will receive your funds within", "errorPage.tryAgain": "Try again", "errorPage.unhandledErrorOccurred": "An unhandled error occurred!", "errors.auth.adminOnly": "Only admin users can perform this operation.", "errors.auth.permissionDenied": "Permission denied.", "errors.auth.permissionDeniedWithOperation": "You can only perform {operation} for yourself.", "errors.auth.tonWalletRequired": "User does not have a TON wallet address configured.", "errors.auth.unauthenticated": "Authentication required.", "errors.auth.userNotFound": "User not found.", "errors.fulfillAndResell.insufficientBalance": "Insufficient balance to create resell order.", "errors.fulfillAndResell.invalidOrderStatus": "Order must have status \"gift sent to relayer\" to be resold.", "errors.fulfillAndResell.invalidParameters": "Order ID and resell price are required and must be valid.", "errors.fulfillAndResell.notOrderBuyer": "You are not the buyer of this order.", "errors.fulfillAndResell.orderNotFound": "Order not found.", "errors.generic.authenticationFailed": "Authentication failed.", "errors.generic.operationFailed": "Operation failed. Please try again.", "errors.generic.serverError": "Server error occurred.", "errors.generic.unknownError": "An unknown error occurred.", "errors.order.buyerCannotPurchaseSameOrder": "You cannot purchase the same order again.", "errors.order.collectionNotActive": "Collection is not active.", "errors.order.collectionNotFound": "Collection not found.", "errors.order.insufficientBalance": "Insufficient balance.", "errors.order.onlyBuyerCanSetSecondaryPrice": "Only the current buyer can set secondary market price.", "errors.order.onlyPaidOrdersPurchasable": "Only orders with PAID status can be purchased on secondary market.", "errors.order.onlyPaidOrdersSecondaryMarket": "Only orders with PAID status can be listed on secondary market.", "errors.order.orderMustBeGiftSentStatus": "Order must be in gift sent to relayer status to complete purchase.", "errors.order.orderMustBePaidStatus": "Order must be in paid status to send gift to relayer.", "errors.order.orderMustHaveBuyerAndSeller": "Order must have both buyer and seller to be listed on secondary market.", "errors.order.orderNotAvailableSecondaryMarket": "Order is not available on secondary market.", "errors.order.orderNotFound": "Order not found.", "errors.order.secondaryPriceBelowMinimum": "Secondary market price must be at least {minPrice} TON.", "errors.order.secondaryPriceExceedsCollateral": "Secondary market price cannot exceed total collateral of {totalCollateral} TON (buyer: {buyerAmount} TON + seller: {sellerAmount} TON).", "errors.order.sellerCannotPurchaseOwnOrder": "Seller cannot purchase their own order on secondary market.", "errors.telegram.botTokenNotConfigured": "Telegram bot token not configured.", "errors.telegram.firebaseAuthError": "Firebase Auth error occurred.", "errors.telegram.iamPermissionError": "Firebase service account lacks required IAM permissions for custom token creation.", "errors.telegram.initDataRequired": "initData is required.", "errors.telegram.invalidTelegramData": "Invalid Telegram data.", "errors.validation.botTokenRequired": "Bot token is required.", "errors.validation.invalidBotToken": "Invalid bot token.", "errors.validation.invalidCollectionId": "Valid collection ID is required.", "errors.validation.invalidOrderId": "Valid order ID is required.", "errors.validation.invalidPrice": "Valid price is required.", "errors.validation.invalidSecondaryMarketPrice": "Valid secondary market price is required.", "errors.validation.ownedGiftIdRequired": "Owned gift ID is required.", "errors.validation.positiveAmountRequired": "{fieldName} must be greater than 0.", "errors.validation.requiredField": "{field} is required.", "errors.validation.userIdOrTgIdRequired": "Either userId or tgId is required.", "errors.withdrawal.amountAboveMaximum": "Withdrawal amount cannot exceed {maxAmount} TON.", "errors.withdrawal.amountBelowMinimum": "Withdrawal amount must be at least {minAmount} TON.", "errors.withdrawal.amountExceeds24hLimit": "Withdrawal amount exceeds 24-hour limit. You can withdraw up to {remainingLimit} TON. Limit resets at {resetAt}.", "errors.withdrawal.amountTooSmallAfterFees": "Amount too small after fees.", "errors.withdrawal.calculationFailed": "Failed to calculate withdrawal status", "errors.withdrawal.insufficientAvailableBalance": "Insufficient available balance for withdrawal.", "footer.marketplace": "Marketplace", "footer.myOrders": "My Orders", "footer.myProfile": "My Profile", "fulfillAndResellDrawer.availableBalance": "Available balance:", "fulfillAndResellDrawer.cancel": "Cancel", "fulfillAndResellDrawer.fulfillAndResell": "Fulfill & Resell", "fulfillAndResellDrawer.insufficientBalance": "Insufficient balance to lock {amount} TON", "fulfillAndResellDrawer.lockAmount": "Lock amount:", "fulfillAndResellDrawer.lockPercentage": "Lock percentage:", "fulfillAndResellDrawer.processing": "Processing...", "fulfillAndResellDrawer.resellInformation": "Resell Order Information", "fulfillAndResellDrawer.resellPrice": "Resell Price (TON)", "fulfillAndResellDrawer.title": "Fulfill & Resell Order", "freezePeriodStatus.expired": "Expired", "freezePeriodStatus.freezePeriodEnded": "Freeze period has ended", "freezePeriodStatus.freezePeriodNotStarted": "Freeze period hasn't started yet", "freezePeriodStatus.timeRemaining": "{days}d {hours}h {minutes}m {seconds}s remaining", "giftInfoDrawer.claimGiftSteps": "Follow these steps to claim your gift from the relayer", "giftInfoDrawer.claimYourGift": "Claim Your Gift", "giftInfoDrawer.sendGiftSteps": "Follow these steps to send your gift to the relayer", "giftInfoDrawer.sendGiftToRelayer": "Send Gift to <PERSON><PERSON>", "header.deposit": "<PERSON><PERSON><PERSON><PERSON>", "header.profile": "Profile", "header.walletDisconnected": "Wallet disconnected", "header.withdraw": "Withdraw", "insufficientBalance.topUp": "Top up balance", "loginModal.authenticationRequired": "Authentication Required", "loginModal.mustBeLoggedIn": "You must be logged in to perform this action.", "loginModal.signInWithTelegram": "Sign in with Telegram", "loginModal.signingIn": "Signing in...", "marketplace.activity.executedOrdersDescription": "Executed orders will be displayed here", "marketplace.activity.noActivityFound": "No activity found", "marketplace.activity.orderNumber": "Order #{number}", "marketplace.activity.viewOrder": "View order", "marketplace.createOrder.cancel": "Cancel", "marketplace.createOrder.collection": "Collection", "marketplace.createOrder.create": "Create", "marketplace.createOrder.createSellOrder": "Create Sell Order", "marketplace.createOrder.creating": "Creating...", "marketplace.createOrder.enterPrice": "Enter price in TON", "marketplace.createOrder.insufficientBalance": "Insufficient balance", "marketplace.createOrder.maximumPrice": "Maximum price is {amount} TON", "marketplace.createOrder.minimumPrice": "Minimum price is {amount} TON", "marketplace.createOrder.price": "Price", "marketplace.createOrder.selectCollection": "Select collection...", "marketplace.resell.cancel": "Cancel", "marketplace.resell.createResaleOrder": "Create Resale Order", "marketplace.resell.importantNotice": "Important Notice", "marketplace.resell.importantNoticeDescription": "Once you set a resale price, your order will be listed on the secondary market. Other users will be able to purchase it at your set price.", "marketplace.resell.loadingYourOrders": "Loading your orders...", "marketplace.resell.minimumPrice": "Minimum: {minPrice} TON", "marketplace.resell.minimumTonPlaceholder": "Minimum {minPrice} TON", "marketplace.resell.noOrdersFoundToResell": "No orders found that can be resold", "marketplace.resell.originalPrice": "Original Price", "marketplace.resell.resalePriceTon": "Resale Price (TON)", "marketplace.resell.resellMyOrder": "Resell My Order", "marketplace.resell.selectOrderToResell": "Select an order you purchased to resell on the secondary market", "marketplace.resell.setResalePrice": "Set Resale Price", "marketplace.resell.setResalePriceButton": "Set Resale Price", "marketplace.resell.setResalePriceSubtitle": "Set your price for reselling this order on the secondary market", "marketplace.resell.settingPrice": "Setting Price...", "marketplace.resell.tooHigh": "✗ Too high", "marketplace.resell.tooLow": "✗ Too low", "marketplace.resell.updateResaleOrder": "Update Resale Order", "marketplace.resell.valid": "✓ Valid", "marketplace.tabs.activity": "Activity", "marketplace.tabs.buy": "Buy", "marketplace.tabs.sell": "<PERSON>ll", "marketplaceFilters.allCollections": "All Collections", "marketplaceFilters.max": "Max", "marketplaceFilters.min": "Min", "marketplaceFilters.newestFirst": "Newest First", "marketplaceFilters.oldestFirst": "Oldest First", "marketplaceFilters.priceHighToLow": "Price: High to Low", "marketplaceFilters.priceLowToHigh": "Price: Low to High", "marketplaceFilters.sortBy": "Sort by", "mock.message": "Mock message", "notFound.redirecting": "Redirecting...", "notFound.takingYouBackToMarketplace": "Taking you back to the marketplace", "nouns.confirmation": "Confirmation", "nouns.description": "Description", "nouns.email": "Email", "nouns.error": "Error", "nouns.from": "From", "nouns.name": "Name", "nouns.orderNumber": "Order #{number}", "nouns.password": "Password", "nouns.price": "Price", "nouns.service": "Service", "orderActors.anonymousUser": "Anonymous User", "orderActors.buyer": "Buyer", "orderActors.noBuyerAssigned": "No buyer assigned", "orderActors.noRoleAssigned": "No {role} assigned", "orderActors.noSellerAssigned": "No seller assigned", "orderActors.resseller": "<PERSON><PERSON><PERSON>", "orderActors.seller": "<PERSON><PERSON>", "orderDeadlineTimer.deadline": "Deadline", "orderDeadlineTimer.giftWillBecomeTransferable": "Gift will become transferable soon", "orderDeadlineTimer.sellerMustSend": "Seller must send", "orderDeadlineTimer.sendOrLoseCollateral": "Send or lose collateral", "orderDeadlineTimer.waiting": "Waiting", "orderDetails.content.action": "Action", "orderDetails.content.buy": "Buy", "orderDetails.content.fulfill": "Fulfill", "orderDetails.content.insufficientBalance": "Insufficient balance to complete this action", "orderDetails.content.openingTelegram": "Opening Telegram...", "orderDetails.content.share": "Share", "orderDetails.content.showResellHistory": "Show Resell History", "orderDetails.fees.buyer": "Buyer", "orderDetails.fees.collateral": "Collateral", "orderDetails.fees.collateralDescription": "{buyerPercentage}% collateral for buyers. Locked until the order is fulfilled. Instantly refunded if order is unsuccessful.", "orderDetails.fees.deposited": "Deposited", "orderDetails.fees.feePaidBySeller": "Fee {feePercent}%. Paid by seller.", "orderDetails.fees.orderDetailsAndFees": "Order Details & Fees", "orderDetails.fees.purchaseFee": "Purchase Fee", "orderDetails.fees.seller": "<PERSON><PERSON>", "orderDetails.lastUpdate": "Last update", "orderPageClient.failedToLoadOrder": "Failed to load order", "orderPageClient.orderNotFound": "Order not found", "orderPageClient.redirectingToHome": "Redirecting to home...", "orderStatus.active": "Active", "orderStatus.cancelled": "Cancelled", "orderStatus.fulfilled": "Fulfilled", "orderStatus.giftSentToRelayer": "Sen<PERSON> to Bot", "orderStatus.paid": "Paid", "orderStatusUtils.active": "Active", "orderStatusUtils.buyerDeadline": "Buyer Deadline", "orderStatusUtils.buyerMustClaimGiftOrLoseCollateral": "Buyer must claim gift or lose collateral", "orderStatusUtils.cancelled": "Cancelled", "orderStatusUtils.claimGiftFromRelayerOrLoseCollateral": "Claim gift from relayer or lose collateral", "orderStatusUtils.deadline": "Deadline", "orderStatusUtils.fulfilled": "Fulfilled", "orderStatusUtils.giftSentToRelayer": "Sen<PERSON> to Bot", "orderStatusUtils.paid": "Paid", "orderStatusUtils.sellerDeadline": "<PERSON><PERSON>line", "orderStatusUtils.sellerMustSend": "Seller must send", "orderStatusUtils.sellerMustSendGiftOrLoseCollateral": "Seller must send gift or lose collateral", "orderStatusUtils.sendGiftToRelayerOrLoseCollateral": "Send gift to relayer or lose collateral", "orderStatusUtils.sendOrLoseCollateral": "Send or lose collateral", "orderStatusUtils.timeToClaimGift": "Time to <PERSON><PERSON><PERSON>", "orderStatusUtils.timeToSendGift": "Time to Send Gift", "orders.cancelOrder.cancel": "Cancel", "orders.cancelOrder.cancelOrder": "Cancel Order", "orders.cancelOrder.cancellationWarning": "This action cannot be undone.", "orders.cancelOrder.cancelling": "Cancelling...", "orders.cancelOrder.collateralLossWarning": "You will lose {amount} TON in collateral.", "orders.cancelOrder.confirmCancellation": "Are you sure you want to cancel this order?", "orders.cancelOrder.failedToCancelOrder": "Failed to cancel order: {message}", "orders.cancelOrder.keepOrder": "Keep Order", "orders.cancelOrder.orderCancelledSuccessfully": "Order cancelled successfully", "orders.cancelOrder.resellerEarningsWarning": "As a reseller, you will lose your potential earnings.", "orders.cancelOrder.unexpectedError": "An unexpected error occurred", "orders.clickLoginToSeeOrders": "Click on login Telegram button to see your orders", "orders.noBuyOrdersFound": "No buy orders found", "orders.noSellOrdersFound": "No sell orders found", "orders.tabs.myBuyOrders": "My Buy Orders ({count})", "orders.tabs.mySellOrders": "My Sell Orders ({count})", "orders.userOrderCard.getAGift": "Get a Gift", "orders.userOrderCard.resellThisOrder": "Resell this order", "orders.userOrderCard.sendAGift": "Send a Gift", "orders.youAreNotLoggedIn": "You are not logged in", "profile.form.displayName": "Display Name", "profile.form.editProfile": "Edit Profile", "profile.form.enterYourDisplayName": "Enter your display name", "profile.form.failedToUpdateProfile": "Failed to update profile. Please try again.", "profile.form.nameIsRequired": "Name is required", "profile.form.nameTooLong": "Name must be less than 50 characters", "profile.form.profileUpdatedSuccessfully": "Profile updated successfully!", "profile.form.updateProfile": "Update Profile", "profile.form.updating": "Updating...", "profile.main": "Main", "profile.myTransactions": "My Transactions", "profile.referralSection.anonymous": "Anonymous", "profile.referralSection.failedToLoadReferrals": "Failed to load referrals", "profile.referralSection.failedToShareReferralLink": "Failed to share referral link", "profile.referralSection.friends": "friends", "profile.referralSection.joinMeOnMarketplace": "Join me on this amazing marketplace and start earning rewards!", "profile.referralSection.joinTheMarketplace": "Join the Marketplace", "profile.referralSection.loadingReferralData": "Loading referral data...", "profile.referralSection.name": "Name", "profile.referralSection.ofTheirPurchaseFees": "of their purchase fees", "profile.referralSection.points": "points", "profile.referralSection.potentialEarnings": "Potential Earnings", "profile.referralSection.referralLinkCopiedToClipboard": "Referral link copied to clipboard!", "profile.referralSection.referralLinkSharedSuccessfully": "Referral link shared successfully!", "profile.referralSection.referralProgram": "Referral Program", "profile.referralSection.referralRateDescription": "You earn {percentage}% of the purchase fee when your referrals make purchases", "profile.referralSection.shareReferralLink": "Share Referral Link", "profile.referralSection.shareTheLinkGetPoints": "Share the link - get points for gifts!", "profile.referralSection.sharing": "Sharing...", "profile.referralSection.yourReferralRate": "Your Referral Rate", "profile.referralSection.yourReferrals": "Your Referrals ({count})", "profile.settings.animatedCollections": "Animated Collections", "profile.settings.animatedCollectionsDescription": "Enable animated collection previews and effects", "profile.settings.settings": "Settings", "profile.socialLinks.followUs": "Follow Us", "profile.socialLinks.followUsOn": "Follow us on {platform}", "profile.transactionHistory.emptyState.noTransactionsYet": "No transactions yet", "profile.transactionHistory.emptyState.transactionHistoryDescription": "Your transaction history will appear here when you start trading on the marketplace", "profile.transactionHistory.header.beta": "BETA", "profile.transactionHistory.header.refresh": "Refresh", "profile.transactionHistory.header.transactionHistory": "Transaction History", "profile.transactionHistory.loadingState.loadingYourTransactions": "Loading your transactions...", "profile.transactionHistory.pagination.loadingMoreTransactions": "Loading more transactions...", "profile.transactionHistory.pagination.reachedEndOfHistory": "You've reached the end of your transaction history", "profile.transactionHistory.table.amount": "Amount", "profile.transactionHistory.table.date": "Date", "profile.transactionHistory.table.description": "Description", "profile.transactionHistory.table.type": "Type", "profile.transactionHistory.transactionHistory": "Transaction History", "profile.userInfo.anonymousUser": "Anonymous User", "profile.userInfo.availableBalance": "Available Balance", "profile.userInfo.lockedBalance": "Locked Balance", "profile.userInfo.myPoints": "My Points", "profile.userInfo.profileInformation": "Profile Information", "profile.userInfo.totalBalance": "Total Balance", "secondaryMarketBadge.resell": "Resell", "sellButtonComponent.buy": "Buy", "sellPriceDetails.marketFeesIncluded": "market fees included", "tonConnect.authenticating": "Authenticating...", "tonConnect.connect": "Connect", "tonConnect.connecting": "Connecting...", "tonConnect.disconnect": "Disconnect", "userOrderActionsSection.cancelOrder": "Cancel Order", "userOrderActionsSection.createResaleOrder": "Create Resale Order", "userOrderActionsSection.showResellHistory": "Show Resell History", "userOrderActionsSection.updateResaleOrder": "Update Resale Order", "userOrderDeadlineSection.giftWillBecomeTransferableSoon": "Gift will become transferable soon", "userOrderDeadlineSection.waiting": "Waiting", "userOrderStatusAlerts.freezePeriodActive": "Freeze Period Active", "userOrderStatusAlerts.freezePeriodDescription": "Collection items cannot be transferred yet. Wait for the freeze period to end.", "userOrderStatusAlerts.giftReady": "Gift Ready!", "userOrderStatusAlerts.giftReadyDescription": "Your gift has been sent to the relayer. Please visit the bot to claim your gift.", "userOrderStatusAlerts.giftRefundAvailable": "Gift Refund Available", "userOrderStatusAlerts.giftRefundDescription": "Go to the relayer to refund your gift.", "userOrderStatusAlerts.openBotForRefund": "Open Bot for Refund", "userOrderStatusAlerts.openBotToClaim": "Open Bo<PERSON> to <PERSON><PERSON><PERSON>", "userOrderStatusAlerts.readyToSend": "Ready to Send", "userOrderStatusAlerts.readyToSendDescription": "You can now send the gift to the relayer.", "userOrderStatusAlerts.waitingForTransfer": "Waiting for Transfer", "userOrderStatusAlerts.waitingForTransferDescription": "Wait until the collection item becomes transferable.", "withdrawDrawer.amountMustBeAtLeast": "Amount must be at least 1 TON", "withdrawDrawer.availableBalance": "Available balance:", "withdrawDrawer.cancel": "Cancel", "withdrawDrawer.enterAmountToWithdraw": "Enter amount to withdraw", "withdrawDrawer.insufficientAvailableBalance": "Insufficient available balance", "withdrawDrawer.insufficientBalance": "Insufficient available balance", "withdrawDrawer.invalidAmount": "Invalid amount", "withdrawDrawer.invalidWithdrawalAmount": "Invalid withdrawal amount", "withdrawDrawer.limitResetsAt": "Limit resets at:", "withdrawDrawer.loadingConfiguration": "Loading configuration...", "withdrawDrawer.max": "Max", "withdrawDrawer.minTonPlaceholder": "Min 1 TON", "withdrawDrawer.minimumWithdrawal": "Minimum withdrawal:", "withdrawDrawer.minimumWithdrawalAmount": "Minimum withdrawal amount is 1 TON", "withdrawDrawer.netAmount": "Net amount:", "withdrawDrawer.noWalletAddressFound": "No wallet address found in your profile", "withdrawDrawer.pleaseConnectWallet": "Please connect your wallet to make a withdrawal", "withdrawDrawer.pleaseConnectWalletFirst": "Please connect your wallet first", "withdrawDrawer.processing": "Processing...", "withdrawDrawer.remainingLimit": "Remaining limit:", "withdrawDrawer.unexpectedError": "An unexpected error occurred", "withdrawDrawer.withdraw": "Withdraw", "withdrawDrawer.withdrawAmount": "Withdraw amount:", "withdrawDrawer.withdrawAmountTon": "With<PERSON><PERSON> Amount (TON)", "withdrawDrawer.withdrawFunds": "Withdraw Funds", "withdrawDrawer.withdrawTonToWallet": "Withdraw TON to your connected wallet", "withdrawDrawer.withdrawalFailed": "<PERSON><PERSON><PERSON> failed: {message}", "withdrawDrawer.withdrawalFee": "Withdrawal fee:", "withdrawDrawer.withdrawalInformation": "Withdrawal Information", "withdrawDrawer.withdrawalLimit24h": "24-hour withdrawal limit:", "withdrawDrawer.withdrawalSuccessful": "Withdrawal successful! Transaction: {hash}", "withdrawDrawer.youWillReceive": "You will receive:"}