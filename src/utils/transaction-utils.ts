import type { TxType } from '@mikerudenko/marketplace-shared';
import { firebaseTimestampToDate } from '@mikerudenko/marketplace-shared';

import { TX_TYPE_DISPLAY_MAP } from '@/core.constants';

import {
  formatTransactionAmount,
  getTransactionAmountColor,
} from './transaction-sign-utils';

export const formatTxType = (txType: TxType): string => {
  return TX_TYPE_DISPLAY_MAP[txType] || txType;
};

export const formatAmount = (amount: number): string => {
  return formatTransactionAmount(amount);
};

export const getAmountColor = (amount: number): string => {
  return getTransactionAmountColor(amount);
};

export const formatTransactionDate = (date: Date | undefined): string => {
  if (!date) return '';
  return firebaseTimestampToDate(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};
