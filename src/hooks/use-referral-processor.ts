import { useCallback } from 'react';

import { getUserById } from '@/api/auth-api';
import { updateUser } from '@/api/user-api';
import type { UserEntity } from '@/core.constants';
import {
  clearStoredReferralId,
  getStoredReferralId,
} from '@/utils/referral-utils';

export const useReferralProcessor = () => {
  const processReferralForUser = useCallback(
    async (
      user: UserEntity,
      firebaseUid: string,
      onUserUpdated?: (updatedUser: UserEntity) => void,
    ): Promise<UserEntity | null> => {
      try {
        if (!user.referrer_id) {
          const storedReferralId = getStoredReferralId();

          if (storedReferralId && storedReferralId !== user.id) {
            await updateUser(user.id, { referrer_id: storedReferralId });

            clearStoredReferralId();

            const updatedUser = await getUserById(firebaseUid);

            if (updatedUser && onUserUpdated) {
              onUserUpdated(updatedUser);
            }

            return updatedUser;
          }
        }

        return user;
      } catch (error) {
        console.error('Error processing referral ID:', error);
        return user;
      }
    },
    [],
  );

  return {
    processReferralForUser,
  };
};
