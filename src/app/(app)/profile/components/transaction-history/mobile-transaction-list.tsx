import type { UserTxEntity } from '@mikerudenko/marketplace-shared';
import { ArrowDownRight, ArrowUpRight, Clock } from 'lucide-react';

import {
  formatAmount,
  formatTransactionDate,
  formatTxType,
  getAmountColor,
} from '@/utils/transaction-utils';

interface MobileTransactionListProps {
  transactions: UserTxEntity[];
}

export function MobileTransactionList({
  transactions,
}: MobileTransactionListProps) {
  return (
    <div className="md:hidden space-y-3">
      {transactions.map((transaction) => (
        <div
          key={transaction.id}
          className="bg-[#232e3c] border border-[#3a4a5c] rounded-lg p-4 space-y-3 active:bg-[#2a3441] transition-colors"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-[#3a4a5c]/50">
                {transaction.amount >= 0 ? (
                  <ArrowDownRight className="w-4 h-4 text-green-500" />
                ) : (
                  <ArrowUpRight className="w-4 h-4 text-red-500" />
                )}
              </div>
              <div>
                <div className="text-[#f5f5f5] font-medium text-sm">
                  {formatTxType(transaction.tx_type)}
                </div>
                <div className="flex items-center gap-1 text-xs text-[#708499]">
                  <Clock className="w-3 h-3" />
                  {formatTransactionDate(transaction.createdAt)}
                </div>
              </div>
            </div>
            <div
              className={`font-semibold text-lg ${getAmountColor(transaction.amount)}`}
            >
              {formatAmount(transaction.amount)}
            </div>
          </div>

          {transaction.description && (
            <div className="text-[#708499] text-sm break-words pl-11">
              {transaction.description}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
