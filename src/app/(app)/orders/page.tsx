'use client';

import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';

import { MarketplaceOrderList } from '@/components/shared/marketplace-order-list';
import { GiftInfoDrawer } from '@/components/ui/drawer/gift-info-drawer';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { type OrderEntity, UserType } from '@/core.constants';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useScrollPreservation } from '@/hooks/use-scroll-preservation';
import { useRootContext } from '@/root-context';

import { UserOrderDetailsDrawer } from '../../../components/order-details/user-order-details-drawer/user-order-details-drawer';
import { FulfillAndResellDrawer } from './fulfill-and-resell-drawer';
import { useUserOrders } from './hooks/use-user-orders';
import { ordersPageMessages } from './intl/orders-page.messages';
import { MyOrdersTabs, type MyOrdersTabType } from './my-orders-tabs';

export default function OrdersPage() {
  const { formatMessage: t } = useIntl();
  const { currentUser } = useRootContext();
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [showGiftInfoDrawer, setShowGiftInfoDrawer] = useState(false);
  const [showResellDrawer, setShowResellDrawer] = useState(false);
  const [giftDrawerMode, setGiftDrawerMode] = useState<'seller' | 'buyer'>(
    'seller',
  );
  const [activeTab, setActiveTab] = useState<MyOrdersTabType>('buy');

  const {
    buyOrdersState,
    sellOrdersState,
    loadOrders,
    loadMoreOrders,
    getUserRole,
  } = useUserOrders(currentUser?.id);

  // Preserve scroll position when drawer opens/closes
  useScrollPreservation({ isOpen: showOrderDetailsDrawer });

  const loadMoreRef = useInfiniteScroll({
    hasMore:
      activeTab === 'buy' ? buyOrdersState.hasMore : sellOrdersState.hasMore,
    loading:
      activeTab === 'buy'
        ? buyOrdersState.loadingMore
        : sellOrdersState.loadingMore,
    onLoadMore: () => loadMoreOrders(activeTab),
  });

  useEffect(() => {
    if (currentUser?.id) {
      loadOrders('buy');
      loadOrders('sell');
    }
  }, [currentUser?.id, loadOrders]);

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderUpdate = () => {
    if (currentUser?.id) {
      loadOrders('buy');
      loadOrders('sell');
    }
  };

  const handleSendGift = (order: OrderEntity, userType: UserType) => {
    setGiftDrawerMode(userType === UserType.SELLER ? 'seller' : 'buyer');
    setShowGiftInfoDrawer(true);
  };

  const handleResellOrder = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowResellDrawer(true);
  };

  if (!currentUser) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <p className="text-[#708499] text-lg">
            {t(ordersPageMessages.youAreNotLoggedIn)}
          </p>
          <p className="text-[#708499] text-sm">
            {t(ordersPageMessages.clickLoginToSeeOrders)}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 pb-[75px]">
      <Tabs value={activeTab}>
        <MyOrdersTabs
          activeTab={activeTab}
          onTabChange={setActiveTab}
          buyOrdersCount={buyOrdersState.orders.length}
          sellOrdersCount={sellOrdersState.orders.length}
        />

        <TabsContent value="buy" className="space-y-4">
          <MarketplaceOrderList
            ref={loadMoreRef}
            variant="user-order"
            orders={buyOrdersState.orders}
            loading={buyOrdersState.loading}
            loadingMore={buyOrdersState.loadingMore}
            hasMore={buyOrdersState.hasMore}
            emptyMessage={t(ordersPageMessages.noBuyOrdersFound)}
            onOrderClick={handleOrderClick}
            getUserRole={getUserRole}
            onSendGift={handleSendGift}
            onResellOrder={handleResellOrder}
          />
        </TabsContent>

        <TabsContent value="sell" className="space-y-4">
          <MarketplaceOrderList
            ref={loadMoreRef}
            variant="user-order"
            orders={sellOrdersState.orders}
            loading={sellOrdersState.loading}
            loadingMore={sellOrdersState.loadingMore}
            hasMore={sellOrdersState.hasMore}
            emptyMessage={t(ordersPageMessages.noSellOrdersFound)}
            onOrderClick={handleOrderClick}
            getUserRole={getUserRole}
            onSendGift={handleSendGift}
            onResellOrder={handleResellOrder}
          />
        </TabsContent>
      </Tabs>

      <UserOrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        userType={selectedOrder ? getUserRole(selectedOrder) : UserType.BUYER}
        onOrderUpdate={handleOrderUpdate}
      />

      <GiftInfoDrawer
        open={showGiftInfoDrawer}
        onOpenChange={setShowGiftInfoDrawer}
        mode={giftDrawerMode}
      />

      <FulfillAndResellDrawer
        open={showResellDrawer}
        onOpenChange={setShowResellDrawer}
        order={selectedOrder}
        onOrderResold={handleOrderUpdate}
      />
    </div>
  );
}
