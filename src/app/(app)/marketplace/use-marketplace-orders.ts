import type { OrderEntity } from '@mikerudenko/marketplace-shared';
import type { DocumentSnapshot } from 'firebase/firestore';
import { useCallback, useRef, useState } from 'react';

import {
  getActivityOrders,
  getOrdersForSellers,
  getUnifiedBuyersOrders,
} from '@/api/orders-api';

export type TabType = 'sellers' | 'buyers' | 'activity';
export type SortType = 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';

interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy: SortType;
  currentUserId?: string;
}

interface OrderState {
  orders: OrderEntity[];
  loading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  lastDoc: DocumentSnapshot | null;
}

interface UseMarketplaceOrdersProps {
  activeTab: TabType;
  filters: OrderFilters;
}

interface UseMarketplaceOrdersReturn {
  sellersState: OrderState;
  buyersState: OrderState;
  activityState: OrderState;
  loadOrders: (reset?: boolean) => Promise<void>;
  loadMoreOrders: () => void;
  resetOrders: () => void;
}

const createInitialState = (): OrderState => ({
  orders: [],
  loading: false,
  loadingMore: false,
  hasMore: true,
  lastDoc: null,
});

const addOrdersWithoutDuplicates = (
  existingOrders: OrderEntity[],
  newOrders: OrderEntity[],
  tabType: TabType,
): OrderEntity[] => {
  const existingIds = new Set(existingOrders.map((order) => order.id));
  const filteredNewOrders = newOrders.filter(
    (order) => !existingIds.has(order.id),
  );
  const duplicates = newOrders.filter((order) => existingIds.has(order.id));

  if (duplicates.length > 0) {
    console.warn(
      `🚨 Found duplicate orders for ${tabType}:`,
      duplicates.map((o) => o.id),
    );
  }

  return [...existingOrders, ...filteredNewOrders];
};

export const useMarketplaceOrders = ({
  activeTab,
  filters,
}: UseMarketplaceOrdersProps): UseMarketplaceOrdersReturn => {
  const [sellersState, setSellersState] =
    useState<OrderState>(createInitialState);
  const [buyersState, setBuyersState] =
    useState<OrderState>(createInitialState);
  const [activityState, setActivityState] =
    useState<OrderState>(createInitialState);

  const sellersLastDocRef = useRef<DocumentSnapshot | null>(null);
  const buyersLastDocRef = useRef<DocumentSnapshot | null>(null);
  const activityLastDocRef = useRef<DocumentSnapshot | null>(null);

  const loadOrders = useCallback(
    async (reset = true) => {
      let setState: React.Dispatch<React.SetStateAction<OrderState>>;
      let lastDocRef: React.MutableRefObject<DocumentSnapshot | null>;

      switch (activeTab) {
        case 'sellers':
          setState = setSellersState;
          lastDocRef = sellersLastDocRef;
          break;
        case 'buyers':
          setState = setBuyersState;
          lastDocRef = buyersLastDocRef;
          break;
        case 'activity':
          setState = setActivityState;
          lastDocRef = activityLastDocRef;
          break;
        default:
          return;
      }

      if (reset) {
        setState((prev) => ({
          ...prev,
          loading: true,
          orders: [],
          hasMore: true,
        }));
        lastDocRef.current = null;
      } else {
        setState((prev) => ({ ...prev, loadingMore: true }));
      }

      try {
        const validSortBy =
          typeof filters.sortBy === 'string' &&
          ['price_asc', 'price_desc', 'date_asc', 'date_desc'].includes(
            filters.sortBy,
          )
            ? filters.sortBy
            : 'date_desc';

        const requestFilters = {
          ...filters,
          sortBy: validSortBy,
          limit: 20,
          lastDoc: reset ? null : lastDocRef.current,
        };

        let result;
        switch (activeTab) {
          case 'sellers':
            result = await getOrdersForSellers(requestFilters);
            break;
          case 'buyers':
            result = await getUnifiedBuyersOrders(requestFilters);
            break;
          case 'activity':
            result = await getActivityOrders(requestFilters);
            break;
          default:
            return;
        }

        setState((prev) => {
          if (reset) {
            return {
              ...prev,
              orders: result.orders,
              hasMore: result.hasMore,
            };
          } else {
            return {
              ...prev,
              orders: addOrdersWithoutDuplicates(
                prev.orders,
                result.orders,
                activeTab,
              ),
              hasMore: result.hasMore,
            };
          }
        });

        lastDocRef.current = result.lastDoc;
      } catch (error) {
        console.error('Error loading orders:', error);
      } finally {
        setState((prev) => ({
          ...prev,
          loading: false,
          loadingMore: false,
        }));
      }
    },
    [activeTab, filters],
  );

  const loadMoreOrders = useCallback(() => {
    let currentState: OrderState;
    switch (activeTab) {
      case 'sellers':
        currentState = sellersState;
        break;
      case 'buyers':
        currentState = buyersState;
        break;
      case 'activity':
        currentState = activityState;
        break;
      default:
        return;
    }

    const isLoading = currentState.loading || currentState.loadingMore;

    if (currentState.hasMore && !isLoading) {
      loadOrders(false);
    }
  }, [activeTab, sellersState, buyersState, activityState, loadOrders]);

  const resetOrders = useCallback(() => {
    setSellersState(createInitialState());
    setBuyersState(createInitialState());
    setActivityState(createInitialState());
    sellersLastDocRef.current = null;
    buyersLastDocRef.current = null;
    activityLastDocRef.current = null;
  }, []);

  return {
    sellersState,
    buyersState,
    activityState,
    loadOrders,
    loadMoreOrders,
    resetOrders,
  };
};
