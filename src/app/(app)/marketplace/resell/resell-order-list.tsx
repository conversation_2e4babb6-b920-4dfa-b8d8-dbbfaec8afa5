'use client';

import type {
  CollectionEntity,
  OrderEntity,
} from '@mikerudenko/marketplace-shared';
import { Caption } from '@telegram-apps/telegram-ui';
import { Hash, TrendingUp } from 'lucide-react';

import { OrderImage } from '@/components/shared/order-image';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';

interface ResellOrderListProps {
  orders: OrderEntity[];
  collections: CollectionEntity[];
  onOrderSelect: (order: OrderEntity) => void;
}

export function ResellOrderList({
  orders,
  collections,
  onOrderSelect,
}: ResellOrderListProps) {
  const getCollection = (collectionId: string) => {
    return collections.find((c) => c.id === collectionId);
  };

  return (
    <div className="space-y-3 max-h-[50vh] overflow-y-auto">
      {orders.map((order) => {
        const collection = getCollection(order.collectionId);

        return (
          <div
            key={order.id}
            className="bg-[#232e3c] rounded-xl p-4 border border-[#3a4a5c] hover:border-[#4a5a6c] transition-colors"
          >
            <div className="flex gap-3 mb-3">
              <div className="flex-shrink-0">
                <OrderImage
                  order={order}
                  collection={collection}
                  className="w-16 h-16"
                />
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Hash className="w-4 h-4 text-[#708499]" />
                    <span className="text-sm font-medium text-[#f5f5f5]">
                      {order.number || order.id?.slice(-6)}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <TonLogo size={20} />
                    <span className="text-lg font-bold text-[#f5f5f5]">
                      {order.price.toFixed(2)}
                    </span>
                  </div>
                </div>

                <div>
                  <Caption level="2" weight="3" className="text-[#708499] mb-1">
                    Collection
                  </Caption>
                  <p className="text-[#f5f5f5] font-medium truncate">
                    {collection?.name || 'Unknown Collection'}
                  </p>
                </div>
              </div>
            </div>

            <Button
              onClick={() => onOrderSelect(order)}
              className="w-full bg-[#6ab2f2] hover:bg-[#5a9fd9] text-white rounded-xl py-2 font-semibold flex items-center justify-center gap-2"
            >
              <TrendingUp className="w-4 h-4" />
              Set Resale Price
            </Button>
          </div>
        );
      })}
    </div>
  );
}
