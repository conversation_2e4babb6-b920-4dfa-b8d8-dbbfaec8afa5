'use client';

import type { CollectionEntity } from '@mikerudenko/marketplace-shared';
import {
  CollectionStatus,
  firebaseTimestampToDate,
} from '@mikerudenko/marketplace-shared';
import { Trash2 } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import {
  deleteCollection,
  getCollectionsWithPagination,
} from '@/api/collection-api';
import { TgsOrImage } from '@/components/tgs/tgs-or-image';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { FreezePeriodStatus } from '@/components/ui/freeze-period-status';
import { Label } from '@/components/ui/label';
import { Pagination } from '@/components/ui/pagination';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { COLLECTION_STATUS_TEXT } from '@/core.constants';
import { usePagePagination } from '@/hooks/use-page-pagination';

import { ManageCollectionModal } from './manage-collection-modal';

type StatusBadgeProps = {
  status: CollectionStatus;
};

const StatusBadge = ({ status }: StatusBadgeProps) => {
  const getStatusColor = (status: CollectionStatus) => {
    switch (status) {
      case CollectionStatus.MARKET:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case CollectionStatus.PREMARKET:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case CollectionStatus.DELETED:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <span
      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(
        status,
      )}`}
    >
      {COLLECTION_STATUS_TEXT[status]}
    </span>
  );
};

export const CollectionManagement = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCollection, setEditingCollection] =
    useState<CollectionEntity | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [hideInactive, setHideInactive] = useState(false);

  const fetchCollectionsPage = useCallback(
    async (page: number, pageSize: number) => {
      return await getCollectionsWithPagination(page, pageSize, hideInactive);
    },
    [hideInactive],
  );

  const {
    items: collections,
    loading,
    currentPage,
    totalPages,
    totalItems,
    goToPage,
    loadPage,
    refresh,
  } = usePagePagination<CollectionEntity>(fetchCollectionsPage, {
    pageSize: 25,
  });

  useEffect(() => {
    loadPage(1);
  }, [loadPage]);

  useEffect(() => {
    loadPage(1);
  }, [hideInactive, loadPage]);

  const handleAddCollection = () => {
    setEditingCollection(null);
    setIsModalOpen(true);
  };

  const handleEditCollection = (collection: CollectionEntity) => {
    setEditingCollection(collection);
    setIsModalOpen(true);
  };

  const handleDeleteCollection = async (collection: CollectionEntity) => {
    setIsDeleting(true);
    try {
      await deleteCollection(collection.id);
      refresh();
    } catch (error) {
      console.error('Error deleting collection:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCollectionSaved = () => {
    setIsModalOpen(false);
    setEditingCollection(null);
    refresh();
  };

  // Collections are already filtered server-side based on hideInactive
  const filteredCollections = collections;

  const loadingMarkup = loading ? (
    <div className="flex justify-center py-4">
      <div className="text-sm text-muted-foreground">
        Loading collections...
      </div>
    </div>
  ) : null;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">
          Collection Management
        </h1>
        <Button onClick={handleAddCollection}>Add Collection</Button>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="hide-inactive"
          checked={hideInactive}
          onCheckedChange={setHideInactive}
        />
        <Label htmlFor="hide-inactive" className="text-sm font-medium">
          Hide inactive collections
        </Label>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Image</TableHead>
              <TableHead>Collection ID</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Active</TableHead>
              <TableHead>Freeze Period</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Launched At</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCollections.map((collection) => (
              <TableRow key={collection.id}>
                <TableCell>
                  <div className="relative w-8 h-8 rounded-sm overflow-hidden bg-slate-700 flex-shrink-0">
                    <TgsOrImage
                      isImage={true}
                      collectionId={collection.id}
                      imageProps={{
                        alt: collection.name,
                        fill: true,
                        className: 'object-cover',
                      }}
                    />
                  </div>
                </TableCell>
                <TableCell className="font-mono text-sm">
                  {collection.id}
                </TableCell>
                <TableCell className="font-medium">{collection.name}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        collection.active ? 'bg-green-500' : 'bg-red-500'
                      }`}
                    />
                    <span
                      className={`text-sm ${
                        collection.active
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-red-600 dark:text-red-400'
                      }`}
                    >
                      {collection.active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <FreezePeriodStatus
                    launchedAt={
                      collection.launchedAt
                        ? firebaseTimestampToDate(collection.launchedAt)
                        : null
                    }
                  />
                </TableCell>
                <TableCell>
                  <StatusBadge status={collection.status} />
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {collection.launchedAt
                    ? new Date(
                        firebaseTimestampToDate(collection.launchedAt),
                      ).toLocaleDateString()
                    : 'Not launched'}
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditCollection(collection)}
                      className="cursor-pointer"
                    >
                      Edit
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 cursor-pointer"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Collection</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete &quot;
                            {collection.name}
                            &quot;? This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel className="cursor-pointer">
                            Cancel
                          </AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteCollection(collection)}
                            disabled={isDeleting}
                            className="bg-red-600 hover:bg-red-700 text-white cursor-pointer"
                          >
                            {isDeleting ? 'Deleting...' : 'Delete'}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {loadingMarkup}

      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={goToPage}
          />
        </div>
      )}

      <div className="text-sm text-muted-foreground text-center mt-2">
        Showing {filteredCollections.length} of {totalItems}{' '}
        {hideInactive ? 'active ' : ''}collections
      </div>

      <ManageCollectionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        collection={editingCollection}
        onSave={handleCollectionSaved}
      />
    </div>
  );
};
