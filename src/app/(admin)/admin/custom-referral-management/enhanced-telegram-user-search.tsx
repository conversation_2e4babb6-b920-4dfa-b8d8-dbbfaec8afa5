'use client';

import type { UserEntity } from '@mikerudenko/marketplace-shared';
import { Search, User, X } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import { searchUsersByTelegramHandle } from '@/api/user-api';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { FormDescription, FormLabel } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useDebounce } from '@/hooks/use-debounce';

interface EnhancedTelegramUserSearchProps {
  value: string;
  onUserSelect: (user: UserEntity | null) => void;
  onSearchChange: (value: string) => void;
  selectedUser: UserEntity | null;
}

export const EnhancedTelegramUserSearch = ({
  value,
  onUserSelect,
  onSearchChange,
  selectedUser,
}: EnhancedTelegramUserSearchProps) => {
  const [searchResults, setSearchResults] = useState<UserEntity[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  const debouncedSearchTerm = useDebounce(value, 500);

  const handleSearch = useCallback(async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      setSearchError(null);
      return;
    }

    try {
      setIsSearching(true);
      setSearchError(null);
      const results = await searchUsersByTelegramHandle(searchTerm);
      setSearchResults(results);
      setShowSearchResults(true);

      if (results.length === 0) {
        setSearchError('No users found with this Telegram handle');
      }
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
      setSearchError('Error searching for users');
    } finally {
      setIsSearching(false);
    }
  }, []);

  useEffect(() => {
    if (debouncedSearchTerm && !selectedUser) {
      handleSearch(debouncedSearchTerm);
    }
  }, [debouncedSearchTerm, handleSearch, selectedUser]);

  const handleInputChange = (newValue: string) => {
    onSearchChange(newValue);
    if (selectedUser) {
      onUserSelect(null); // Clear selected user when typing
    }
    if (!newValue.trim()) {
      setShowSearchResults(false);
      setSearchError(null);
    }
  };

  const handleUserSelectInternal = (user: UserEntity) => {
    onUserSelect(user);
    onSearchChange(user.telegram_handle || '');
    setShowSearchResults(false);
    setSearchError(null);
  };

  const handleClearSelection = () => {
    onUserSelect(null);
    onSearchChange('');
    setShowSearchResults(false);
    setSearchError(null);
  };

  const getInitials = (user: UserEntity) => {
    if (user.displayName) {
      return user.displayName
        .split(' ')
        .map((name) => name[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    return user.telegram_handle?.slice(0, 2).toUpperCase() || 'U';
  };

  return (
    <div className="space-y-3">
      <FormLabel>Search by Telegram Handle</FormLabel>

      {selectedUser ? (
        <div className="flex items-center gap-3 p-3 bg-[#232e3c] border border-[#3a4a5c] rounded-lg">
          <Avatar className="h-10 w-10">
            <AvatarImage src={selectedUser.photoURL || undefined} />
            <AvatarFallback className="bg-[#3a4a5c] text-white">
              {getInitials(selectedUser)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="font-medium text-white">
              @{selectedUser.telegram_handle}
            </div>
            <div className="text-sm text-[#708499]">
              {selectedUser.displayName || 'No display name'}
            </div>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleClearSelection}
            className="text-[#708499] hover:text-white"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <div className="relative">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#708499]" />
            <Input
              placeholder="Enter telegram handle (e.g., username)"
              value={value}
              onChange={(e) => handleInputChange(e.target.value)}
              className="pl-10 border-[#3a4a5c] bg-[#232e3c] text-white placeholder:text-[#708499]"
            />
            {isSearching && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin h-4 w-4 border-2 border-[#708499] border-t-white rounded-full"></div>
              </div>
            )}
          </div>

          {showSearchResults && searchResults.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-[#232e3c] border border-[#3a4a5c] rounded-lg shadow-lg max-h-60 overflow-y-auto">
              {searchResults.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center gap-3 px-3 py-3 hover:bg-[#3a4a5c] cursor-pointer transition-colors"
                  onClick={() => handleUserSelectInternal(user)}
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user.photoURL || undefined} />
                    <AvatarFallback className="bg-[#3a4a5c] text-white text-xs">
                      {getInitials(user)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium text-white">
                      @{user.telegram_handle}
                    </div>
                    <div className="text-sm text-[#708499]">
                      {user.displayName || 'No display name'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {searchError && (
            <div className="mt-2 text-sm text-red-400 flex items-center gap-2">
              <User className="h-4 w-4" />
              {searchError}
            </div>
          )}
        </div>
      )}

      <FormDescription className="text-[#708499]">
        Search for a user by their Telegram handle. User must be found to set
        custom referral fee.
      </FormDescription>
    </div>
  );
};
