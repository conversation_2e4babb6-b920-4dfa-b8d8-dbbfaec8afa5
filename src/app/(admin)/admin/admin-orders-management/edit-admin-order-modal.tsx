import type {
  CollectionEntity,
  OrderEntity,
} from '@mikerudenko/marketplace-shared';
import { useEffect, useState } from 'react';
import { useLocalStorage } from 'usehooks-ts';

import type { EditOrderData } from '@/api/admin-api';
import { editOrder } from '@/api/admin-api';
import { Button } from '@/components/ui/button';
import { CollectionSelect } from '@/components/ui/collection-select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LocalStorageKeys } from '@/core.constants';
import { useToast } from '@/hooks/use-toast';

interface EditAdminOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => Promise<void>;
  order: OrderEntity | null;
  collections: CollectionEntity[];
}

interface EditFormData {
  number: string;
  price: string;
  collectionId: string;
}

export function EditAdminOrderModal({
  isOpen,
  onClose,
  onSuccess,
  order,
  collections,
}: EditAdminOrderModalProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );
  const [formData, setFormData] = useState<EditFormData>({
    number: '',
    price: '',
    collectionId: '',
  });

  // Initialize form data when order changes
  useEffect(() => {
    if (order) {
      setFormData({
        number: order.number?.toString() || '',
        price: order.price?.toString() || '',
        collectionId: order.collectionId || '',
      });
    }
  }, [order]);

  const handleInputChange = (field: keyof EditFormData, value: string) => {
    if (field === 'number' || field === 'price') {
      // Allow only numbers and decimal points for price, only integers for number
      const regex = field === 'price' ? /^[0-9]*\.?[0-9]*$/ : /^[0-9]*$/;
      if (value === '' || regex.test(value)) {
        setFormData((prev) => ({
          ...prev,
          [field]: value,
        }));
      }
    } else {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const validateForm = (): string | null => {
    const number = parseInt(formData.number);
    const price = parseFloat(formData.price);

    if (isNaN(number) || number <= 0) {
      return 'Order number must be a valid number greater than 0';
    }

    if (isNaN(price) || price <= 0) {
      return 'Price must be a valid number greater than 0';
    }

    if (!formData.collectionId) {
      return 'Collection must be selected';
    }

    // Check if collection exists
    const collection = collections.find((c) => c.id === formData.collectionId);
    if (!collection) {
      return 'Selected collection is not valid';
    }

    // Check if price meets floor price
    if (price < collection.floorPrice) {
      return `Price must be at least ${collection.floorPrice} TON (collection floor price)`;
    }

    return null;
  };

  const hasChanges = (): boolean => {
    if (!order) return false;

    return (
      parseInt(formData.number) !== order.number ||
      parseFloat(formData.price) !== order.price ||
      formData.collectionId !== order.collectionId
    );
  };

  const handleSubmit = async () => {
    if (!order) return;

    const validationError = validateForm();
    if (validationError) {
      toast({
        title: 'Validation Error',
        description: validationError,
        variant: 'destructive',
      });
      return;
    }

    if (!hasChanges()) {
      toast({
        title: 'No Changes',
        description: 'No changes were made to the order',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);

    try {
      const updates: EditOrderData = {};

      if (parseInt(formData.number) !== order.number) {
        updates.number = parseInt(formData.number);
      }

      if (parseFloat(formData.price) !== order.price) {
        updates.price = parseFloat(formData.price);
      }

      if (formData.collectionId !== order.collectionId) {
        updates.collectionId = formData.collectionId;
      }

      await editOrder(order.id!, updates);

      toast({
        title: 'Success',
        description: 'Order updated successfully',
      });

      await onSuccess();
      onClose();
    } catch (error) {
      console.error('Error updating order:', error);
      toast({
        title: 'Error',
        description: 'Failed to update order',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  if (!order) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Order #{order.number}</DialogTitle>
          <DialogDescription>
            Update order details. Changes will be applied immediately.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="orderNumber" className="text-right">
              Order Number
            </Label>
            <Input
              id="orderNumber"
              type="text"
              value={formData.number}
              onChange={(e) => handleInputChange('number', e.target.value)}
              className="col-span-3"
              placeholder="e.g., 123"
              disabled={loading}
              readOnly
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="price" className="text-right">
              Price (TON)
            </Label>
            <Input
              id="price"
              type="text"
              value={formData.price}
              onChange={(e) => handleInputChange('price', e.target.value)}
              className="col-span-3"
              placeholder="e.g., 5.50"
              disabled={loading}
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="collection" className="text-right">
              Collection
            </Label>
            <div className="col-span-3">
              <CollectionSelect
                hideLabel
                animated={isAnimatedCollection}
                collections={collections}
                value={formData.collectionId}
                onValueChange={(value) =>
                  handleInputChange('collectionId', value)
                }
                placeholder="Select collection"
                className="w-full"
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading || !hasChanges()}>
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
